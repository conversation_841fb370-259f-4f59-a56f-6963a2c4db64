"""
使用adata API获取前复权日线数据并入库
Created on 2025-08-17
@author: stock-analysis-engine
"""
import pandas as pd
import adata
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool
import warnings
import pathlib
from serverchan_sdk import sc_send
from urllib3.exceptions import ConnectTimeoutError
from requests.exceptions import RequestException
import concurrent.futures
import threading
import queue
import random

# 先导入日志配置
from log_config import setup_logger, log_progress, log_error, log_warning, send_notification
# 再导入交易日历模块
from trade_calendar import is_trade_day, get_latest_trade_day, get_next_trade_day

# 屏蔽警告信息
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 初始化环境变量
load_dotenv()

# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志系统
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', '31490600'),
    'host': os.getenv('DB_HOST', '**********'),
    'port': os.getenv('DB_PORT', '3306'),
    'database': os.getenv('DB_NAME', 'instockdb'),
    'charset': 'utf8mb4'
}

# 并发处理配置
ENABLE_RATE_LIMIT = False  # 禁用API频率限制，使用并发处理
CONCURRENT_WORKERS = 20  # 并发工作线程数
CONCURRENT_PROXY_COUNT = 20  # 并发代理数量
STOCKS_PER_WORKER = 300  # 每个工作线程处理的股票数量
WORKER_DELAY = 0.001  # 工作线程之间的延迟（秒）

# 代理配置
USE_PROXY = True  # 是否使用代理
PROXY_API_URL = "http://api2.xkdaili.com/tools/XApi.ashx?apikey=XK3504946A8EE9E2DD63&qty=1&format=txt&split=0&sign=b42c2ea9f006a445c8affae05fef04e3&area=330000"
PROXY_POOL = []  # 代理池
PROXY_LOCK = threading.Lock()  # 代理池锁
PROXY_FAILURE_THRESHOLD = 3  # 代理失败阈值
PROXY_EXPIRY_TIME = 3 * 60  # 代理有效期（秒）
PROXY_ACQUIRED_TIMES = {}  # 代理获取时间字典


# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_daily` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `pre_close` DECIMAL(10,4) COMMENT '昨收价',
    `change` DECIMAL(10,4) COMMENT '涨跌额',
    `pct_chg` DECIMAL(10,4) COMMENT '涨跌幅',
    `vol` DECIMAL(20,4) COMMENT '成交量(手)',
    `amount` DECIMAL(20,4) COMMENT '成交额(千元)',
    `adj_factor` DECIMAL(10,4) COMMENT '复权因子',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_ts_code_trade_date` (`ts_code`, `trade_date`),
    KEY `idx_stock_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票日线行情表(前复权)';
"""

# 全局数据库引擎
_DB_ENGINE = None

def get_db_engine():
    """获取数据库引擎（单例模式）"""
    global _DB_ENGINE
    if _DB_ENGINE is None:
        _DB_ENGINE = create_engine(
            f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
            f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_pre_ping=True,
            pool_recycle=3600,  # 连接回收时间
            echo=False  # 生产环境关闭SQL日志
        )
    return _DB_ENGINE

@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = get_db_engine()
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise

def retry_api_call(func, *args, max_retries=5, initial_delay=30, **kwargs):
    """通用API调用重试函数"""
    retry_count = 0
    max_wait_time = 300  # 最大等待时间5分钟

    while True:  # 无限重试，直到成功
        try:
            result = func(*args, **kwargs)
            return result

        except (ConnectTimeoutError, RequestException) as e:
            retry_count += 1
            
            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0  # 重置重试计数

            log_progress(logger, f"连接超时, 第{retry_count}次重试, 等待{wait_time}秒")
            time.sleep(wait_time)

        except Exception as e:
            error_msg = str(e)

            # 处理API限制
            if "每分钟最多访问该接口" in error_msg or "接口限制" in error_msg or "返回空数据" in error_msg:
                if USE_PROXY:
                    log_progress(logger, "检测到API限制，尝试获取新代理...")
                    if get_new_proxy():
                        log_progress(logger, "已获取新代理，继续重试")
                        continue
                    else:
                        log_error(logger, "获取新代理失败，等待30秒后重试")
                        time.sleep(30)
                        continue
                else:
                    log_error(logger, "检测到API限制，等待60秒后重试")
                    time.sleep(60)
                    continue

            retry_count += 1
            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0

            log_error(logger, f"API调用失败: {error_msg}, 等待{wait_time}秒后重试")
            time.sleep(wait_time)

def get_new_proxy():
    """获取新的代理服务器并添加到代理池"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES
    
    try:
        import requests
        
        # 发送请求获取代理
        response = requests.get(PROXY_API_URL, timeout=10)
        if response.status_code == 200:
            proxy = response.text.strip()
            if proxy:
                with PROXY_LOCK:
                    if proxy not in PROXY_POOL:
                        PROXY_POOL.append(proxy)
                        PROXY_ACQUIRED_TIMES[proxy] = time.time()
                        return True
            return False
        else:
            return False
            
    except Exception as e:
        return False

def is_proxy_expired(proxy):
    """检查代理是否已过期"""
    global PROXY_ACQUIRED_TIMES
    
    if not USE_PROXY or proxy not in PROXY_ACQUIRED_TIMES:
        return True
    
    current_time = time.time()
    elapsed_time = current_time - PROXY_ACQUIRED_TIMES[proxy]
    
    return elapsed_time >= PROXY_EXPIRY_TIME

def setup_proxy():
    """初始化代理池"""
    global PROXY_POOL
    
    if not USE_PROXY:
        return True
    
    # 如果代理池为空，获取新代理
    if not PROXY_POOL:
        return get_new_proxy()
    
    return True

def get_proxy_from_pool():
    """从代理池获取一个可用的代理"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES
    
    if not USE_PROXY:
        return None
    
    with PROXY_LOCK:
        # 清理过期代理
        expired_proxies = [p for p in PROXY_POOL if is_proxy_expired(p)]
        for proxy in expired_proxies:
            PROXY_POOL.remove(proxy)
            del PROXY_ACQUIRED_TIMES[proxy]
        
        # 如果代理池为空，获取新代理
        if not PROXY_POOL:
            get_new_proxy()
        
        # 随机选择一个代理
        if PROXY_POOL:
            proxy = random.choice(PROXY_POOL)
            return proxy
    
    return None

def remove_proxy_from_pool(proxy):
    """从代理池中移除代理"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES
    
    if not USE_PROXY or proxy not in PROXY_POOL:
        return
    
    with PROXY_LOCK:
        if proxy in PROXY_POOL:
            PROXY_POOL.remove(proxy)
        
        if proxy in PROXY_ACQUIRED_TIMES:
            del PROXY_ACQUIRED_TIMES[proxy]

def get_stock_codes_from_db():
    """从数据库获取所有股票代码"""
    try:
        with db_connection() as conn:
            query = text("SELECT stock_code, name FROM stock_basic")
            result = conn.execute(query)
            stock_codes = [(row[0], row[1]) for row in result.fetchall()]
            log_progress(logger, f"从数据库获取到 {len(stock_codes)} 只股票代码")
            return stock_codes
    except Exception as e:
        log_error(logger, f"获取股票代码失败: {str(e)}")
        raise

def get_stock_daily_data_adata(stock_code, start_date=None, end_date=None):
    """使用adata API获取单只股票的前复权日线数据"""
    try:
        def _get_data():
            # 确保代理已设置
            if USE_PROXY and not setup_proxy():
                raise Exception("无法设置代理")
            
            # 从代理池获取代理
            proxy = None
            if USE_PROXY:
                proxy = get_proxy_from_pool()
                if proxy:
                    adata.proxy(is_proxy=True, ip=proxy)
            
            # 调用adata API获取前复权日线数据
            df = adata.stock.market.get_market(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                k_type=1,  # 日K
                adjust_type=1  # 前复权
            )
            if df is None or df.empty:
                raise Exception(f"获取股票 {stock_code} 的日线数据返回空数据")
            return df
        
        df = retry_api_call(_get_data)
        return df
    
    except Exception as e:
        log_error(logger, f"获取股票 {stock_code} 日线数据失败: {str(e)}")
        # 如果是代理问题，尝试获取新代理
        if USE_PROXY and ("接口限制" in str(e) or "返回空数据" in str(e)):
            get_new_proxy()
        raise

def validate_daily_data(df):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    try:
        # 转换日期格式
        if 'trade_date' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_date']).dt.date
        elif 'trade_time' in df.columns:
            df['trade_date'] = pd.to_datetime(df['trade_time']).dt.date
        
        # 批量数值类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'pre_close',
                          'change', 'change_pct', 'volume', 'amount', 'turnover_ratio']

        existing_numeric_cols = [col for col in numeric_columns if col in df.columns]
        if existing_numeric_cols:
            df[existing_numeric_cols] = df[existing_numeric_cols].apply(pd.to_numeric, errors='coerce')

        # 删除重复项
        if 'stock_code' in df.columns and 'trade_date' in df.columns:
            df = df.drop_duplicates(subset=['stock_code', 'trade_date'], keep='first')

        # 过滤无效数据
        required_columns = ['stock_code', 'trade_date']
        existing_required_cols = [col for col in required_columns if col in df.columns]
        if existing_required_cols:
            valid_df = df.dropna(subset=existing_required_cols)
        else:
            valid_df = df

        if len(valid_df) < len(df):
            log_progress(logger, f"数据清洗：从 {len(df)} 条减少到 {len(valid_df)} 条有效记录")

        return valid_df

    except Exception as e:
        log_error(logger, f"数据验证失败: {str(e)}")
        raise

def transform_adata_to_ts_format(df, stock_code, stock_name):
    """将adata API返回的数据转换为ts_stock_daily表格式"""
    if df.empty:
        return df
    
    # 创建数据框的副本
    df = df.copy()
    
    # 添加基本信息
    df['stock_code'] = stock_code
    df['name'] = stock_name
    df['ts_code'] = stock_code  # 暂时使用stock_code作为ts_code
    
    # 字段映射
    column_mapping = {
        'change_pct': 'pct_chg',
        'volume': 'vol'
    }
    
    # 重命名列
    for old_col, new_col in column_mapping.items():
        if old_col in df.columns:
            df = df.rename(columns={old_col: new_col})
    
    # 确保必要的列存在
    required_columns = ['ts_code', 'stock_code', 'name', 'trade_date', 
                       'open', 'high', 'low', 'close', 'pre_close',
                       'change', 'pct_chg', 'vol', 'amount']
    
    for col in required_columns:
        if col not in df.columns:
            if col in ['ts_code', 'stock_code', 'name']:
                # 这些列必须有值
                df[col] = ''
            else:
                # 数值列可以设为0
                df[col] = 0.0
    
    # 添加adj_factor列，adata API返回的是前复权数据，设为1.0
    df['adj_factor'] = 1.0
    
    # 只保留需要的列
    df = df[required_columns + ['adj_factor']]
    
    return df

def worker_process_stocks(stock_batch, worker_id, start_date, end_date, result_queue, failed_queue):
    """工作线程处理一批股票数据"""
    try:
        log_progress(logger, f"工作线程 {worker_id} 开始处理 {len(stock_batch)} 只股票")
        
        success_count = 0
        for i, (stock_code, stock_name) in enumerate(stock_batch):
            try:
                # 获取单只股票的日线数据
                df = get_stock_daily_data_adata(stock_code, start_date, end_date)
                
                if not df.empty:
                    # 数据转换
                    transformed_df = transform_adata_to_ts_format(df, stock_code, stock_name)
                    
                    # 数据清洗
                    clean_df = validate_daily_data(transformed_df)
                    
                    if not clean_df.empty:
                        result_queue.put(clean_df)
                        success_count += 1
                    else:
                        failed_queue.put((stock_code, stock_name))
                else:
                    failed_queue.put((stock_code, stock_name))
                
                # 每处理100只股票报告一次进度
                if (i + 1) % 100 == 0:
                    log_progress(logger, f"工作线程 {worker_id} 已处理 {i+1}/{len(stock_batch)} 只股票")
                
                # 添加小延迟，避免过于频繁的请求
                time.sleep(WORKER_DELAY)
                
            except Exception as e:
                log_error(logger, f"工作线程 {worker_id} 处理股票 {stock_code} 失败: {str(e)}")
                failed_queue.put((stock_code, stock_name))
                continue
        
        log_progress(logger, f"工作线程 {worker_id} 完成处理，成功: {success_count}/{len(stock_batch)}")
        
    except Exception as e:
        log_error(logger, f"工作线程 {worker_id} 发生错误: {str(e)}")
        raise

def get_and_save_daily_data():
    """获取和保存股票日线数据（并发版本）"""
    try:
        # 获取所有股票代码
        stock_codes = get_stock_codes_from_db()
        
        # 计算日期范围(近2年)
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=730)).strftime('%Y-%m-%d')
        
        log_progress(logger, f"开始获取日线数据,股票数量: {len(stock_codes)},日期范围: {start_date} 至 {end_date}")
        
        # 初始化代理池
        if USE_PROXY:
            log_progress(logger, f"初始化代理池，获取 {CONCURRENT_PROXY_COUNT} 个代理...")
            for _ in range(CONCURRENT_PROXY_COUNT):
                get_new_proxy()
                time.sleep(0.5)  # 避免过于频繁的请求
        
        # 创建结果队列和失败队列
        result_queue = queue.Queue()
        failed_queue = queue.Queue()
        
        # 将股票代码分成多个批次
        stock_batches = []
        for i in range(0, len(stock_codes), STOCKS_PER_WORKER):
            batch = stock_codes[i:i+STOCKS_PER_WORKER]
            stock_batches.append(batch)
        
        log_progress(logger, f"将 {len(stock_codes)} 只股票分成 {len(stock_batches)} 个批次，每个批次 {STOCKS_PER_WORKER} 只股票")
        
        # 使用线程池并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=CONCURRENT_WORKERS) as executor:
            futures = []
            
            for i, batch in enumerate(stock_batches):
                future = executor.submit(worker_process_stocks, batch, i+1, start_date, end_date, result_queue, failed_queue)
                futures.append(future)
                
                # 添加小延迟，避免所有线程同时启动
                time.sleep(0.1)
            
            # 等待所有任务完成
            concurrent.futures.wait(futures)
        
        # 收集结果
        all_data = []
        while not result_queue.empty():
            all_data.append(result_queue.get())
        
        # 收集失败的股票
        failed_stocks = []
        while not failed_queue.empty():
            failed_stocks.append(failed_queue.get())
        
        # 合并所有数据
        if all_data:
            final_df = pd.concat(all_data, ignore_index=True)
            log_progress(logger, f"数据合并完成，共 {len(final_df)} 条记录")
            
            # 保存数据
            save_daily_data(final_df)
            
            # 报告失败的股票
            if failed_stocks:
                log_warning(logger, f"以下股票获取失败: {failed_stocks}")
            
            log_progress(logger, f"所有数据处理完成，成功处理 {len(final_df)} 条记录，失败 {len(failed_stocks)} 只股票")
        else:
            log_warning(logger, "未获取到任何有效数据")
        
    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise

def save_daily_data(df):
    """保存数据到数据库"""
    import time

    if df.empty:
        log_progress(logger, "无数据需要保存")
        return

    start_time = time.time()
    max_retries = 3
    batch_size = 50000  # 增大批次大小以提高写入速度
    total_records = len(df)
    processed_records = 0

    log_progress(logger, f"开始保存数据到数据库，总记录数: {total_records:,}，批次大小: {batch_size:,}")

    # 先清空表数据
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE ts_stock_daily"))
            log_progress(logger, "已清空 ts_stock_daily 表数据")
    except Exception as e:
        log_error(logger, f"清空表数据失败: {str(e)}")
        raise

    # 按批次保存数据
    total_batches = (total_records + batch_size - 1) // batch_size

    for i in range(0, total_records, batch_size):
        batch_start_time = time.time()
        batch_df = df.iloc[i:i+batch_size]
        batch_num = i // batch_size + 1
        retry_count = 0

        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        'ts_stock_daily',
                        conn,
                        if_exists='append',
                        index=False,
                        method='multi',
                        chunksize=5000  # 增大内部分块大小以提高写入速度
                    )

                    processed_records += len(batch_df)
                    batch_time = time.time() - batch_start_time

                    # 进度报告
                    if batch_num % 5 == 0 or processed_records == total_records:
                        progress_pct = processed_records / total_records * 100
                        elapsed_time = time.time() - start_time
                        avg_speed = processed_records / elapsed_time if elapsed_time > 0 else 0

                        log_progress(logger,
                            f"批次 {batch_num}/{total_batches}: {processed_records:,}/{total_records:,} "
                            f"({progress_pct:.1f}%) - 批次耗时: {batch_time:.2f}s, 平均速度: {avg_speed:.0f}条/秒")

                    break  # 保存成功，跳出重试循环

            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(logger, f"批次 {batch_num} 写入失败，第 {retry_count} 次重试: {str(e)}")
                    time.sleep(retry_count)  # 递增等待时间
                else:
                    log_error(logger, f"批次 {batch_num} 写入失败，已重试 {max_retries} 次: {str(e)}")
                    raise

    total_time = time.time() - start_time
    avg_speed = processed_records / total_time if total_time > 0 else 0
    log_progress(logger, f"数据保存完成: {processed_records:,} 条记录，总耗时: {total_time:.2f}秒，平均速度: {avg_speed:.0f}条/秒")

def main():
    """主函数"""
    import time

    start_time = time.time()
    log_progress(logger, "=== 股票日线数据同步(adata API)开始 ===")

    try:
        # 初始化数据库
        init_start = time.time()
        init_database()
        init_time = time.time() - init_start
        log_progress(logger, f"数据库初始化完成，耗时 {init_time:.2f} 秒")

        # 获取和保存日线数据
        data_start = time.time()
        get_and_save_daily_data()
        data_time = time.time() - data_start

        # 总结
        total_time = time.time() - start_time
        log_progress(logger, f"=== 股票日线数据同步(adata API)完成 ===")
        log_progress(logger, f"数据处理耗时: {data_time:.2f} 秒，总耗时: {total_time:.2f} 秒")

        return True

    except Exception as e:
        total_time = time.time() - start_time
        error_msg = str(e)
        log_error(logger, f"程序执行异常 (耗时 {total_time:.2f} 秒): {error_msg}")

        send_notification(
            message=f"日线数据同步(adata API)失败: {error_msg}",
            title="日线数据同步(adata API)失败",
            tags="日线数据|失败"
        )
        return False

if __name__ == '__main__':
    import sys

    success = main()
    if not success:
        sys.exit(1)