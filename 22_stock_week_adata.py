import concurrent.futures
import os
import queue
import random
import threading
import time
import warnings
from contextlib import contextmanager
from datetime import datetime, timedelta

import adata
import pandas as pd
from dotenv import load_dotenv
from requests.exceptions import RequestException
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool
from urllib3.exceptions import ConnectTimeoutError

from log_config import setup_logger, log_progress, log_error, send_notification

# 导入交易日历模块
from trade_calendar import is_trade_day, get_friday_of_current_week

# 屏蔽警告信息
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 初始化环境变量
load_dotenv()

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 配置日志
logger = setup_logger(script_name)

# 数据库连接配置
DB_CONFIG = {
    "user": os.getenv("DB_USER", "root"),
    "password": os.getenv("DB_PASSWORD", "31490600"),
    "host": os.getenv("DB_HOST", "**********"),
    "port": os.getenv("DB_PORT", "3306"),
    "database": os.getenv("DB_NAME", "instockdb"),
    "charset": "utf8mb4",
}

# adata API配置
ADATA_TOKEN = os.getenv("ADATA_TOKEN", "your_actual_token_here")

# 并发处理配置
CONCURRENT_WORKERS = 2  # 并发工作线程数
STOCKS_PER_WORKER = 3000  # 每个工作线程处理的股票数量
WORKER_DELAY = 0.01  # 工作线程之间的延迟（秒）

# 代理配置
USE_PROXY = False  # 是否使用代理
PROXY_API_URL = "http://api2.xkdaili.com/tools/XApi.ashx?apikey=XK3504946A8EE9E2DD63&qty=1&format=txt&split=0&sign=b42c2ea9f006a445c8affae05fef04e3&area=330000"
PROXY_POOL = []  # 代理池
PROXY_LOCK = threading.Lock()  # 代理池锁
PROXY_FAILURE_THRESHOLD = 3  # 代理失败阈值
PROXY_EXPIRY_TIME = 3 * 60  # 代理有效期（秒）
PROXY_ACQUIRED_TIMES = {}  # 代理获取时间字典

# 数据库连接池配置
DB_POOL_SIZE = 10
DB_MAX_OVERFLOW = 20
DB_POOL_TIMEOUT = 30

# 表结构定义
TABLE_SCHEMA = """
CREATE TABLE IF NOT EXISTS `ts_stock_weekly` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `ts_code` VARCHAR(10) NOT NULL COMMENT 'TS代码',
    `stock_code` VARCHAR(6) NOT NULL COMMENT '股票代码',
    `name` VARCHAR(50) NOT NULL COMMENT '股票名称',
    `trade_date` DATE NOT NULL COMMENT '交易日期',
    `open` DECIMAL(10,4) COMMENT '开盘价(前复权)',
    `high` DECIMAL(10,4) COMMENT '最高价(前复权)',
    `low` DECIMAL(10,4) COMMENT '最低价(前复权)',
    `close` DECIMAL(10,4) COMMENT '收盘价(前复权)',
    `vol` DECIMAL(20,2) COMMENT '成交量',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY `uk_stock_code_trade_date` (`stock_code`, `trade_date`),
    KEY `idx_ts_code` (`stock_code`),
    KEY `idx_trade_date` (`trade_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票周线行情表(前复权)';
"""


def get_new_proxy():
    """获取新的代理服务器并添加到代理池"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES

    try:
        import requests

        # 发送请求获取代理
        response = requests.get(PROXY_API_URL, timeout=10)
        if response.status_code == 200:
            proxy = response.text.strip()
            if proxy:
                with PROXY_LOCK:
                    if proxy not in PROXY_POOL:
                        PROXY_POOL.append(proxy)
                        PROXY_ACQUIRED_TIMES[proxy] = time.time()
                        return True
            return False
        else:
            return False

    except Exception as e:
        return False


def is_proxy_expired(proxy):
    """检查代理是否已过期"""
    global PROXY_ACQUIRED_TIMES

    if not USE_PROXY or proxy not in PROXY_ACQUIRED_TIMES:
        return True

    current_time = time.time()
    elapsed_time = current_time - PROXY_ACQUIRED_TIMES[proxy]

    return elapsed_time >= PROXY_EXPIRY_TIME


def setup_proxy():
    """初始化代理池"""
    global PROXY_POOL

    if not USE_PROXY:
        return True

    # 如果代理池为空，获取新代理
    if not PROXY_POOL:
        return get_new_proxy()

    return True


def get_proxy_from_pool():
    """从代理池获取一个可用的代理"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES

    if not USE_PROXY:
        return None

    with PROXY_LOCK:
        # 清理过期代理
        expired_proxies = [p for p in PROXY_POOL if is_proxy_expired(p)]
        for proxy in expired_proxies:
            PROXY_POOL.remove(proxy)
            del PROXY_ACQUIRED_TIMES[proxy]

        # 如果代理池为空，获取新代理
        if not PROXY_POOL:
            get_new_proxy()

        # 随机选择一个代理
        if PROXY_POOL:
            proxy = random.choice(PROXY_POOL)
            return proxy

    return None


def remove_proxy_from_pool(proxy):
    """从代理池中移除代理"""
    global PROXY_POOL, PROXY_ACQUIRED_TIMES

    if not USE_PROXY or proxy not in PROXY_POOL:
        return

    with PROXY_LOCK:
        if proxy in PROXY_POOL:
            PROXY_POOL.remove(proxy)

        if proxy in PROXY_ACQUIRED_TIMES:
            del PROXY_ACQUIRED_TIMES[proxy]


@contextmanager
def db_connection():
    """数据库连接上下文管理"""
    engine = create_engine(
        f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@"
        f"{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}?charset={DB_CONFIG['charset']}",
        poolclass=QueuePool,
        pool_size=DB_POOL_SIZE,
        max_overflow=DB_MAX_OVERFLOW,
        pool_timeout=DB_POOL_TIMEOUT,
        pool_pre_ping=True,  # 在使用连接前先测试连接是否有效
    )
    conn = None
    try:
        conn = engine.connect()
        yield conn
    except SQLAlchemyError as e:
        log_error(logger, f"数据库连接失败: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()
        engine.dispose()


def init_database():
    """初始化数据库表结构"""
    try:
        with db_connection() as conn:
            conn.execute(text(TABLE_SCHEMA))
            log_progress(logger, "数据库表初始化完成")
    except Exception as e:
        log_error(logger, f"数据库初始化失败: {str(e)}")
        raise


def retry_api_call(func, *args, max_retries=5, initial_delay=30, **kwargs):
    """通用API调用重试函数"""
    retry_count = 0
    max_wait_time = 300  # 最大等待时间5分钟

    while True:  # 无限重试，直到成功
        try:
            result = func(*args, **kwargs)
            return result

        except (ConnectTimeoutError, RequestException) as e:
            retry_count += 1

            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0  # 重置重试计数

            log_progress(logger, f"连接超时, 第{retry_count}次重试, 等待{wait_time}秒")
            time.sleep(wait_time)

        except Exception as e:
            error_msg = str(e)

            # 处理API限制 - 立即换代理
            if (
                "每分钟最多访问该接口" in error_msg
                or "接口限制" in error_msg
                or "返回空数据" in error_msg
            ):
                if USE_PROXY:
                    log_progress(logger, "检测到API限制，立即获取新代理...")
                    # 强制获取新代理，无论是否成功都继续重试
                    get_new_proxy()
                    log_progress(logger, "已尝试获取新代理，立即重试")
                    continue  # 立即重试，不等待
                else:
                    log_error(logger, "检测到API限制，等待60秒后重试")
                    time.sleep(60)
                    continue

            retry_count += 1
            wait_time = min(initial_delay * (2 ** (retry_count - 1)), max_wait_time)

            if retry_count >= max_retries:
                wait_time = max_wait_time
                retry_count = 0

            log_error(logger, f"API调用失败: {error_msg}, 等待{wait_time}秒后重试")
            time.sleep(wait_time)


def get_stocks():
    """从数据库获取所有上市公司列表"""
    try:
        with db_connection() as conn:
            query = text("SELECT ts_code, stock_code, name FROM stock_basic")
            df = pd.read_sql(query, conn)

            if df is None or df.empty:
                raise Exception("获取股票列表返回空数据")

            return df
    except Exception as e:
        log_error(logger, f"获取股票列表失败: {str(e)}")
        raise


def validate_weekly_data(df, last_trading_day=None):
    """数据校验与清洗"""
    if df.empty:
        return df

    # 创建数据框的副本
    df = df.copy()

    # 转换日期格式
    df["trade_date"] = pd.to_datetime(df["trade_date"]).dt.date

    # 如果提供了本周最后交易日，则将本周数据的交易日期替换为实际的最后交易日
    if last_trading_day:
        # 将last_trading_day转换为date类型
        last_date = datetime.strptime(last_trading_day, "%Y%m%d").date()
        # 获取当前周的周五日期
        current_friday = get_friday_of_current_week(datetime.now()).date()

        # 只替换本周数据的日期（通过比较trade_date是否等于本周五来识别）
        df.loc[df["trade_date"] == current_friday, "trade_date"] = last_date
        log_progress(
            logger,
            f"已将本周{len(df[df['trade_date'] == last_date])}条数据的交易日期更新为实际的最后交易日: {last_date}",
        )

    # 数值类型转换
    numeric_cols = ["close", "open", "high", "low", "volume"]
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors="coerce")

    # 删除重复项
    df = df.drop_duplicates(subset=["stock_code", "trade_date"], keep="first")

    # 确保所有必要的列都存在
    required_columns = ["stock_code", "trade_date"]
    for col in required_columns:
        if col not in df.columns:
            log_error(logger, f"数据中缺少必要的列: {col}")
            raise Exception(f"数据中缺少必要的列: {col}")

    return df.dropna(subset=required_columns)


def get_trade_calendar(start_date, end_date):
    """获取交易日历，筛选出每周最后一个交易日（周线日期）"""
    try:
        # 使用adata API获取交易日历
        df = adata.stock.market.get_trade_cal(
            exchange="SSE", start_date=start_date, end_date=end_date
        )

        if df is None or df.empty:
            raise Exception("获取交易日历返回空数据")

        # 筛选出开市日
        trading_days = df[df["is_open"] == 1]["cal_date"].tolist()

        # 转换为日期对象以便处理
        trading_days = [datetime.strptime(day, "%Y%m%d") for day in trading_days]
        trading_days.sort()

        # 识别每周最后一个交易日
        weekly_last_days = []
        for i in range(len(trading_days)):
            # 如果是列表中的最后一个日期，或者下一个日期与当前日期不在同一周，则当前日期是本周最后一个交易日
            if (
                i == len(trading_days) - 1
                or trading_days[i].isocalendar()[1]
                != trading_days[i + 1].isocalendar()[1]
            ):
                weekly_last_days.append(trading_days[i].strftime("%Y%m%d"))

        return weekly_last_days
    except Exception as e:
        log_error(logger, f"获取交易日历失败: {str(e)}")
        raise


def get_weekly_data_for_stocks(stock_codes, start_date, end_date, worker_id):
    """为指定股票代码列表获取周线数据"""
    all_weekly_data = []
    total_stocks = len(stock_codes)
    processed_count = 0
    start_time = time.time()

    for i, ts_code in enumerate(stock_codes):
        try:
            # 确保代理已设置
            if USE_PROXY and not setup_proxy():
                raise Exception("无法设置代理")

            # 从代理池获取代理
            proxy = None
            if USE_PROXY:
                proxy = get_proxy_from_pool()
                if proxy:
                    adata.proxy(is_proxy=True, ip=proxy)

            # 使用adata API获取周线数据
            # 从ts_code中提取stock_code和交易所
            if "." in ts_code:
                stock_code, exchange = ts_code.split(".")
            else:
                stock_code = ts_code
                exchange = "SH"  # 默认上海交易所

            # 转换日期格式从YYYYMMDD到YYYY-MM-DD
            start_date_formatted = (
                f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            )
            end_date_formatted = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

            df = adata.stock.market.get_market(
                stock_code=stock_code,
                start_date=start_date_formatted,
                end_date=end_date_formatted,
                k_type=2,  # 2代表周K线
                adjust_type=1,  # 1代表前复权
            )

            if df is not None and not df.empty:
                # 转换数据格式以适应我们的数据库表结构
                df_transformed = df.copy()

                # 添加ts_code列（将stock_code与交易所后缀组合）
                df_transformed["ts_code"] = stock_code + "." + exchange

                # 重命名volume列为vol
                if "volume" in df_transformed.columns:
                    df_transformed["vol"] = df_transformed["volume"]

                all_weekly_data.append(df_transformed)

            processed_count += 1

            # 实时进度日志（顺序模式/详细）
            percent = (processed_count / total_stocks) * 100 if total_stocks else 0
            elapsed = time.time() - start_time
            speed = processed_count / elapsed if elapsed > 0 else 0
            remaining = max(total_stocks - processed_count, 0)
            eta_sec = int(remaining / speed) if speed > 0 else 0
            eta_min, eta_s = divmod(eta_sec, 60)
            log_progress(
                logger,
                f"进度: {processed_count}/{total_stocks} ({percent:.2f}%) | 已耗时 {int(elapsed)//60:02d}:{int(elapsed)%60:02d} | 速度 {speed:.2f}/s | 预计剩余 {eta_min:02d}:{eta_s:02d} | 当前: {ts_code}",
            )

            # 添加小延迟，避免请求过于频繁
            time.sleep(WORKER_DELAY)

        except Exception as e:
            log_error(
                logger,
                f"工作线程 {worker_id}: 获取股票 {ts_code} 周线数据失败: {str(e)}",
            )
            # 如果是代理问题，尝试获取新代理
            if USE_PROXY and ("接口限制" in str(e) or "返回空数据" in str(e)):
                get_new_proxy()
            continue

    # 合并所有股票的周线数据
    if all_weekly_data:
        return pd.concat(all_weekly_data, ignore_index=True)
    return pd.DataFrame()


def worker_process_stocks(stock_codes, start_date, end_date, worker_id, result_queue):
    """工作线程处理股票数据"""
    try:
        log_progress(logger, f"工作线程 {worker_id} 开始处理 {len(stock_codes)} 只股票")

        # 获取周线数据
        df_weekly = get_weekly_data_for_stocks(
            stock_codes, start_date, end_date, worker_id
        )

        # 将结果放入队列
        result_queue.put((worker_id, df_weekly))

        log_progress(
            logger, f"工作线程 {worker_id} 完成，获取到 {len(df_weekly)} 条记录"
        )

    except Exception as e:
        log_error(logger, f"工作线程 {worker_id} 处理失败: {str(e)}")
        result_queue.put((worker_id, pd.DataFrame()))


def truncate_table():
    """清空数据表"""
    try:
        with db_connection() as conn:
            conn.execute(text("TRUNCATE TABLE ts_stock_weekly"))
            log_progress(logger, "数据表已清空")
    except Exception as e:
        log_error(logger, f"清空数据表失败: {str(e)}")
        raise


def save_weekly_data(df):
    """保存数据到数据库"""
    if df.empty:
        return

    max_retries = 3
    # 每批写入的记录数
    batch_size = 50000
    total_records = len(df)
    processed_records = 0

    log_progress(
        logger,
        f"开始保存数据到数据库，总记录数: {total_records}，批次大小: {batch_size}",
    )

    # 按批次保存数据
    for i in range(0, total_records, batch_size):
        batch_df = df.iloc[i : i + batch_size]
        retry_count = 0

        while retry_count < max_retries:
            try:
                with db_connection() as conn:
                    batch_df.to_sql(
                        "ts_stock_weekly",
                        conn,
                        if_exists="append",
                        index=False,
                        chunksize=5000,  # 内部分块大小
                    )

                    processed_records += len(batch_df)
                    percent = (
                        (processed_records / total_records) * 100
                        if total_records
                        else 0
                    )
                    log_progress(
                        logger,
                        f"保存进度: {processed_records}/{total_records} ({percent:.2f}%)",
                    )

                    break  # 保存成功，跳出重试循环

            except SQLAlchemyError as e:
                retry_count += 1
                if retry_count < max_retries:
                    log_progress(
                        logger,
                        f"第 {i//batch_size + 1} 批数据写入失败，正在进行第 {retry_count} 次重试: {str(e)}",
                    )
                    time.sleep(1)
                else:
                    log_error(
                        logger,
                        f"第 {i//batch_size + 1} 批数据写入失败，已重试 {max_retries} 次: {str(e)}",
                    )
                    raise

    log_progress(logger, f"数据保存完成，共写入 {processed_records} 条记录")


def get_and_save_weekly_data():
    """获取和保存股票周线数据"""
    try:
        # 获取所有上市公司列表
        stocks = get_stocks()

        # 计算日期范围(近4年)
        today = datetime.now()
        end_date = today.strftime("%Y%m%d")  # 先使用当前日期获取已有数据
        start_date = (today - timedelta(days=2500)).strftime("%Y%m%d")

        # 获取所有交易日历
        log_progress(logger, "获取交易日历...")
        trading_calendar = get_trade_calendar(start_date, end_date)

        # 检查当前周的最后交易日
        current_weekday = today.weekday()  # 0-6 表示周一到周日
        log_progress(logger, f"当前是周{current_weekday + 1}")

        # 使用专业的交易日历模块检查本周最后一个交易日
        log_progress(logger, "使用交易日历模块检查本周交易日...")

        # 1. 获取本周周五的日期作为API查询日期
        friday_of_week = get_friday_of_current_week(today)
        friday_date_str = friday_of_week.strftime("%Y%m%d")
        log_progress(
            logger, f"本周周五是: {friday_of_week.strftime('%Y-%m-%d')} (用于API查询)"
        )

        # 2. 获取本周实际的最后交易日（用于数据库存储）
        last_trading_day = None
        check_date = friday_of_week
        monday_of_week = friday_of_week - timedelta(days=4)  # 本周一

        # 从周五向前查找，但不超出本周范围
        while check_date >= monday_of_week:
            if is_trade_day(check_date):
                last_trading_day = check_date.strftime("%Y%m%d")
                log_progress(
                    logger,
                    f"找到本周最后交易日: {check_date.strftime('%Y-%m-%d')} (周{check_date.weekday() + 1})",
                )
                break
            check_date -= timedelta(days=1)

        if not last_trading_day:
            log_progress(logger, "本周没有找到交易日")

        # 统一使用周五日期查询API
        query_date = friday_date_str

        # 确保周五日期在查询列表中
        if query_date not in trading_calendar:
            trading_calendar.append(query_date)
            trading_calendar.sort()
            log_progress(logger, f"已将本周周五日期 {query_date} 添加到查询列表")

        log_progress(logger, f"获取到 {len(trading_calendar)} 个可用交易日期")

        # 初始化代理池（顺序模式仅获取一个代理）
        if USE_PROXY:
            log_progress(logger, "初始化代理池（顺序模式），获取 1 个代理...")
            get_new_proxy()
            time.sleep(0.2)  # 轻微间隔，避免过于频繁的请求

        # 顺序获取周线数据（无并发）
        stock_codes = stocks["ts_code"].tolist()
        log_progress(logger, f"开始顺序获取周线数据，共 {len(stock_codes)} 只股票")
        df_weekly = get_weekly_data_for_stocks(stock_codes, start_date, end_date, 1)
        if df_weekly is not None and not df_weekly.empty:
            log_progress(logger, f"周线数据获取完成，共 {len(df_weekly)} 条记录")
        else:
            raise Exception("未获取到周线数据")

        # 添加股票代码和名称
        log_progress(logger, "开始添加股票信息...")

        # 合并股票基本信息
        log_progress(
            logger,
            f"周线数据中共有 {len(df_weekly['stock_code'].unique())} 个唯一股票代码",
        )

        # 使用merge而不是join，明确指定如何合并
        # 重置索引以避免合并问题
        df_weekly = df_weekly.reset_index(drop=True)
        stocks_subset = stocks[["ts_code", "stock_code", "name"]].reset_index(drop=True)

        # 由于API返回的数据中已经有了ts_code，我们需要在合并前删除它，使用stock_basic表中的ts_code
        if "ts_code" in df_weekly.columns:
            df_weekly.drop("ts_code", axis=1, inplace=True)

        df_weekly = pd.merge(
            df_weekly,
            stocks_subset,
            on="stock_code",
            how="inner",  # 使用inner join，只保留当前存在的股票
        )

        # 确保合并后的DataFrame包含所有必要的列
        required_columns = [
            "ts_code",
            "stock_code",
            "name",
            "trade_date",
            "open",
            "high",
            "low",
            "close",
            "volume",
        ]
        for col in required_columns:
            if col not in df_weekly.columns:
                log_error(logger, f"合并后的数据缺少必要的列: {col}")
                raise Exception(f"合并后的数据缺少必要的列: {col}")

        # 数据清洗和保存
        log_progress(logger, "开始数据清洗...")
        clean_df = validate_weekly_data(df_weekly, last_trading_day)

        # 只保留需要的列
        columns_to_save = [
            "ts_code",
            "stock_code",
            "name",
            "trade_date",
            "open",
            "high",
            "low",
            "close",
            "vol",
        ]
        clean_df = clean_df[columns_to_save]

        # 检查最终是否还有空值
        null_check = clean_df[clean_df.isnull().any(axis=1)]
        if not null_check.empty:
            log_progress(
                logger, f"完成处理后仍有 {len(null_check)} 条含空值的记录，将被过滤"
            )
            clean_df = clean_df.dropna()

        # 保存数据前清空旧数据
        truncate_table()
        log_progress(logger, "已清空周线数据表，准备写入新数据")
        # 保存数据
        log_progress(logger, "开始保存数据...")
        save_weekly_data(clean_df)

        log_progress(logger, f"所有数据处理完成，共处理 {len(clean_df)} 条记录")

    except Exception as e:
        log_error(logger, f"数据获取过程发生错误: {str(e)}")
        raise


if __name__ == "__main__":
    try:
        # 移除开始时的通知
        # send_notification(
        #     message="开始获取股票周线数据...",
        #     title="周线数据同步开始",
        #     tags="周线数据|开始"
        # )

        init_database()
        get_and_save_weekly_data()

        # 移除成功时的通知
        # send_notification(
        #     message="股票周线数据同步已完成",
        #     title="周线数据同步成功",
        #     tags="周线数据|完成"
        # )
    except Exception as e:
        error_msg = str(e)
        log_error(logger, f"程序执行异常: {error_msg}")
        send_notification(
            message=f"周线数据同步失败: {error_msg}",
            title="周线数据同步异常",
            tags="周线数据|异常",
        )
        exit(1)
